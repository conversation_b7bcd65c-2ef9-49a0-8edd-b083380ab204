/* أنماط بطاقات لوحة التحكم المدمجة */

/* البطاقات الأساسية المدمجة */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px 8px 0 0;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

.card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin: 0;
}

.card-title i {
  color: #ff6b35;
  font-size: 0.9rem;
}

.card-body {
  padding: 0;
}

.card-actions {
  display: flex;
  gap: 0.4rem;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(229, 231, 235, 0.3);
}

/* بطاقات لوحة التحكم المتقدمة */
.advanced-dashboard {
  padding: 0;
  background: transparent;
}

.dashboard-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dashboard-title i {
  color: #ff6b35;
  font-size: 1.5em;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.current-time {
  font-size: 1rem;
  opacity: 0.9;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(180deg);
}

/* شبكة مؤشرات الأداء المدمجة */
.advanced-kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.advanced-kpi-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.advanced-kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.advanced-kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.advanced-kpi-card.orders-kpi::before {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.advanced-kpi-card.materials-kpi::before {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.advanced-kpi-card.production-kpi::before {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kpi-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  font-size: 0.9rem;
}

.orders-kpi .kpi-icon {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.materials-kpi .kpi-icon {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.production-kpi .kpi-icon {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.kpi-header h3 {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  font-size: 0.65rem;
  font-weight: 600;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
}

.kpi-trend.positive {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.kpi-trend.negative {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.kpi-alert {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  font-size: 0.65rem;
  font-weight: 600;
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.kpi-content {
  text-align: center;
}

.main-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e3c72;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.kpi-breakdown {
  display: flex;
  justify-content: space-around;
  gap: 0.4rem;
}

.breakdown-item {
  text-align: center;
  flex: 1;
}

.breakdown-item .label {
  display: block;
  font-size: 0.65rem;
  color: #6c757d;
  margin-bottom: 0.15rem;
}

.breakdown-item .value {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.breakdown-item .value.pending {
  color: #f39c12;
}

.breakdown-item .value.progress {
  color: #3498db;
}

.breakdown-item .value.completed {
  color: #27ae60;
}

.breakdown-item .value.reserved {
  color: #9b59b6;
}

.breakdown-item .value.warning {
  color: #e74c3c;
}

.breakdown-item .value.active {
  color: #3498db;
}

.breakdown-item .value.efficiency {
  color: #27ae60;
}

/* شاشة التحميل */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #6c757d;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #1e3c72;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .advanced-kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.5rem;
  }

  .dashboard-header {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .dashboard-title {
    font-size: 1.25rem;
  }

  .advanced-kpi-card {
    padding: 0.6rem;
  }

  .main-value {
    font-size: 1.25rem;
  }

  .kpi-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .kpi-header h3 {
    font-size: 0.75rem;
  }

  .kpi-breakdown {
    flex-direction: column;
    gap: 0.3rem;
  }

  .breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .breakdown-item .label,
  .breakdown-item .value {
    display: inline;
    margin: 0;
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .advanced-kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.4rem;
  }

  .advanced-kpi-card {
    padding: 0.5rem;
  }

  .main-value {
    font-size: 1.1rem;
  }

  .kpi-icon {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .kpi-header h3 {
    font-size: 0.7rem;
  }

  .breakdown-item .label,
  .breakdown-item .value {
    font-size: 0.6rem;
  }
}
