/* شبكات مدمجة لجميع صفحات التطبيق */

/* شبكة البطاقات الأساسية */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة البطاقات الصغيرة */
.small-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

/* شبكة البطاقات الكبيرة */
.large-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.25rem;
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة العملاء */
.customers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة الطلبات */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة المواد */
.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة العمال */
.workers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(190px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة المخزون */
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة التقارير */
.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة الفواتير */
.invoices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* شبكة المصروفات */
.expenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* بطاقة مدمجة عامة */
.compact-card {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.compact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  border-color: #1e3c72;
}

.compact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

/* رأس البطاقة المدمجة */
.compact-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.4rem;
  border-bottom: 1px solid #f8f9fa;
}

.compact-card-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.compact-card-title i {
  font-size: 0.8rem;
  color: #ff6b35;
}

.compact-card-badge {
  font-size: 0.65rem;
  padding: 0.15rem 0.4rem;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
}

.badge-primary {
  background: rgba(30, 60, 114, 0.1);
  color: #1e3c72;
}

.badge-success {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.badge-warning {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.badge-danger {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.badge-info {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* جسم البطاقة المدمجة */
.compact-card-body {
  margin-bottom: 0.5rem;
}

.compact-card-text {
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 0.3rem;
}

.compact-card-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.3rem;
}

.compact-card-subtitle {
  font-size: 0.65rem;
  color: #6c757d;
  font-weight: 500;
}

/* ذيل البطاقة المدمجة */
.compact-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.4rem;
  border-top: 1px solid #f8f9fa;
}

.compact-card-actions {
  display: flex;
  gap: 0.3rem;
}

.compact-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.65rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.2rem;
}

.compact-btn-primary {
  background: #1e3c72;
  color: white;
}

.compact-btn-primary:hover {
  background: #2a5298;
  color: white;
  text-decoration: none;
}

.compact-btn-secondary {
  background: #6c757d;
  color: white;
}

.compact-btn-secondary:hover {
  background: #5a6268;
  color: white;
  text-decoration: none;
}

.compact-btn-outline {
  background: transparent;
  border: 1px solid #1e3c72;
  color: #1e3c72;
}

.compact-btn-outline:hover {
  background: #1e3c72;
  color: white;
  text-decoration: none;
}

/* معلومات إضافية مدمجة */
.compact-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.65rem;
  color: #6c757d;
  margin-top: 0.3rem;
}

.compact-info-item {
  display: flex;
  align-items: center;
  gap: 0.2rem;
}

.compact-info-item i {
  font-size: 0.6rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .cards-grid,
  .small-cards-grid,
  .large-cards-grid,
  .stats-grid,
  .customers-grid,
  .orders-grid,
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .reports-grid,
  .invoices-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.5rem;
  }
  
  .compact-card {
    padding: 0.6rem;
  }
  
  .compact-card-title {
    font-size: 0.75rem;
  }
  
  .compact-card-value {
    font-size: 1rem;
  }
  
  .compact-card-text,
  .compact-card-subtitle {
    font-size: 0.7rem;
  }
  
  .compact-btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.6rem;
  }
}

@media (max-width: 480px) {
  .cards-grid,
  .small-cards-grid,
  .large-cards-grid,
  .stats-grid,
  .customers-grid,
  .orders-grid,
  .materials-grid,
  .workers-grid,
  .inventory-grid,
  .reports-grid,
  .invoices-grid,
  .expenses-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.4rem;
  }
  
  .compact-card {
    padding: 0.5rem;
  }
  
  .compact-card-header {
    margin-bottom: 0.4rem;
    padding-bottom: 0.3rem;
  }
  
  .compact-card-body {
    margin-bottom: 0.4rem;
  }
  
  .compact-card-footer {
    padding-top: 0.3rem;
  }
}
